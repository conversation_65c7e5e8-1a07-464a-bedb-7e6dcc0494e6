import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Feature Flag System
///
/// **Task 4.1.4: Implement feature flags for safe rollout**
///
/// This system provides comprehensive feature flag management for safe migration
/// rollout following Context7 MCP patterns for controlled feature deployment.
///
/// Features:
/// - Dynamic feature flag management with real-time updates
/// - Gradual rollout capabilities with percentage-based deployment
/// - User-based feature targeting with criteria matching
/// - A/B testing support with statistical tracking
/// - Emergency rollback with instant flag disabling
/// - Performance monitoring and analytics integration
/// - Developer override capabilities for testing
/// - Persistent flag storage with cloud synchronization
/// - Context7 MCP compliance with dependency injection
/// - Comprehensive logging and audit trails

/// Feature Flag Configuration
class FeatureFlagConfig {
  final String key;
  final String name;
  final String description;
  final bool defaultValue;
  final FeatureFlagType type;
  final Map<String, dynamic> metadata;
  final DateTime? expiresAt;
  final List<FeatureFlagRule> rules;

  const FeatureFlagConfig({
    required this.key,
    required this.name,
    required this.description,
    required this.defaultValue,
    required this.type,
    this.metadata = const {},
    this.expiresAt,
    this.rules = const [],
  });

  factory FeatureFlagConfig.fromJson(Map<String, dynamic> json) {
    return FeatureFlagConfig(
      key: json['key'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      defaultValue: json['defaultValue'] as bool,
      type: FeatureFlagType.values.byName(json['type'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt'] as String) : null,
      rules:
          (json['rules'] as List<dynamic>?)
              ?.map((rule) => FeatureFlagRule.fromJson(rule as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'name': name,
      'description': description,
      'defaultValue': defaultValue,
      'type': type.name,
      'metadata': metadata,
      'expiresAt': expiresAt?.toIso8601String(),
      'rules': rules.map((rule) => rule.toJson()).toList(),
    };
  }
}

/// Feature Flag Type
enum FeatureFlagType { boolean, percentage, userBased, abTest, killSwitch }

/// Feature Flag Rule
class FeatureFlagRule {
  final String condition;
  final dynamic value;
  final int priority;
  final Map<String, dynamic> criteria;

  const FeatureFlagRule({
    required this.condition,
    required this.value,
    required this.priority,
    this.criteria = const {},
  });

  factory FeatureFlagRule.fromJson(Map<String, dynamic> json) {
    return FeatureFlagRule(
      condition: json['condition'] as String,
      value: json['value'],
      priority: json['priority'] as int,
      criteria: json['criteria'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {'condition': condition, 'value': value, 'priority': priority, 'criteria': criteria};
  }
}

/// Feature Flag Context
class FeatureFlagContext {
  final String userId;
  final String deviceId;
  final String appVersion;
  final String platform;
  final Map<String, dynamic> userAttributes;
  final Map<String, dynamic> deviceAttributes;

  const FeatureFlagContext({
    required this.userId,
    required this.deviceId,
    required this.appVersion,
    required this.platform,
    this.userAttributes = const {},
    this.deviceAttributes = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'deviceId': deviceId,
      'appVersion': appVersion,
      'platform': platform,
      'userAttributes': userAttributes,
      'deviceAttributes': deviceAttributes,
    };
  }
}

/// Migration Feature Flags
///
/// **Context7 MCP Feature Flag Definitions:**
/// - Unified notification provider rollout
/// - Legacy provider deprecation timeline
/// - Migration utilities availability
/// - Dashboard and monitoring features
/// - Emergency rollback capabilities
class MigrationFeatureFlags {
  // Core migration flags
  static const String unifiedProviderEnabled = 'unified_provider_enabled';
  static const String legacyProviderDeprecated = 'legacy_provider_deprecated';
  static const String migrationUtilitiesEnabled = 'migration_utilities_enabled';

  // UI and dashboard flags
  static const String migrationDashboardEnabled = 'migration_dashboard_enabled';
  static const String deprecationWarningsEnabled = 'deprecation_warnings_enabled';
  static const String visualIndicatorsEnabled = 'visual_indicators_enabled';

  // Safety and rollback flags
  static const String emergencyRollbackEnabled = 'emergency_rollback_enabled';
  static const String backupCreationEnabled = 'backup_creation_enabled';
  static const String validationEnabled = 'validation_enabled';

  // Analytics and monitoring flags
  static const String analyticsTrackingEnabled = 'analytics_tracking_enabled';
  static const String performanceMonitoringEnabled = 'performance_monitoring_enabled';
  static const String usageTrackingEnabled = 'usage_tracking_enabled';

  /// Get all migration feature flag configurations
  static List<FeatureFlagConfig> getAllConfigs() {
    return [
      // Core migration flags
      const FeatureFlagConfig(
        key: unifiedProviderEnabled,
        name: 'Unified Provider Enabled',
        description: 'Enable the unified notification provider system',
        defaultValue: false,
        type: FeatureFlagType.percentage,
        metadata: {'category': 'core', 'impact': 'high', 'rolloutPercentage': 0},
        rules: [
          FeatureFlagRule(condition: 'percentage', value: 0, priority: 1, criteria: {'rolloutPercentage': 0}),
        ],
      ),

      const FeatureFlagConfig(
        key: legacyProviderDeprecated,
        name: 'Legacy Provider Deprecated',
        description: 'Mark legacy providers as deprecated with warnings',
        defaultValue: false,
        type: FeatureFlagType.boolean,
        metadata: {'category': 'deprecation', 'impact': 'medium'},
      ),

      const FeatureFlagConfig(
        key: migrationUtilitiesEnabled,
        name: 'Migration Utilities Enabled',
        description: 'Enable migration utilities and CLI tools',
        defaultValue: true,
        type: FeatureFlagType.boolean,
        metadata: {'category': 'tools', 'impact': 'low'},
      ),

      // UI and dashboard flags
      const FeatureFlagConfig(
        key: migrationDashboardEnabled,
        name: 'Migration Dashboard Enabled',
        description: 'Enable migration progress dashboard in debug mode',
        defaultValue: kDebugMode,
        type: FeatureFlagType.boolean,
        metadata: {'category': 'ui', 'impact': 'low', 'debugOnly': true},
      ),

      const FeatureFlagConfig(
        key: deprecationWarningsEnabled,
        name: 'Deprecation Warnings Enabled',
        description: 'Show deprecation warnings for legacy provider usage',
        defaultValue: true,
        type: FeatureFlagType.boolean,
        metadata: {'category': 'warnings', 'impact': 'low'},
      ),

      const FeatureFlagConfig(
        key: visualIndicatorsEnabled,
        name: 'Visual Indicators Enabled',
        description: 'Show visual deprecation indicators in debug mode',
        defaultValue: kDebugMode,
        type: FeatureFlagType.boolean,
        metadata: {'category': 'ui', 'impact': 'low', 'debugOnly': true},
      ),

      // Safety and rollback flags
      const FeatureFlagConfig(
        key: emergencyRollbackEnabled,
        name: 'Emergency Rollback Enabled',
        description: 'Enable emergency rollback capabilities',
        defaultValue: true,
        type: FeatureFlagType.killSwitch,
        metadata: {'category': 'safety', 'impact': 'critical'},
      ),

      const FeatureFlagConfig(
        key: backupCreationEnabled,
        name: 'Backup Creation Enabled',
        description: 'Create backups before migration operations',
        defaultValue: true,
        type: FeatureFlagType.boolean,
        metadata: {'category': 'safety', 'impact': 'high'},
      ),

      const FeatureFlagConfig(
        key: validationEnabled,
        name: 'Validation Enabled',
        description: 'Enable validation after migration operations',
        defaultValue: true,
        type: FeatureFlagType.boolean,
        metadata: {'category': 'safety', 'impact': 'high'},
      ),

      // Analytics and monitoring flags
      const FeatureFlagConfig(
        key: analyticsTrackingEnabled,
        name: 'Analytics Tracking Enabled',
        description: 'Enable analytics tracking for migration events',
        defaultValue: false,
        type: FeatureFlagType.userBased,
        metadata: {'category': 'analytics', 'impact': 'low'},
        rules: [
          FeatureFlagRule(condition: 'userAttribute', value: true, priority: 1, criteria: {'analyticsOptIn': true}),
        ],
      ),

      const FeatureFlagConfig(
        key: performanceMonitoringEnabled,
        name: 'Performance Monitoring Enabled',
        description: 'Enable performance monitoring for migration operations',
        defaultValue: kDebugMode,
        type: FeatureFlagType.boolean,
        metadata: {'category': 'monitoring', 'impact': 'low'},
      ),

      const FeatureFlagConfig(
        key: usageTrackingEnabled,
        name: 'Usage Tracking Enabled',
        description: 'Track usage patterns for migration analytics',
        defaultValue: false,
        type: FeatureFlagType.percentage,
        metadata: {'category': 'analytics', 'impact': 'low', 'rolloutPercentage': 10},
        rules: [
          FeatureFlagRule(condition: 'percentage', value: 10, priority: 1, criteria: {'rolloutPercentage': 10}),
        ],
      ),
    ];
  }
}

/// Feature Flag Service
///
/// **Context7 MCP Implementation:**
/// - Single responsibility: Feature flag management and evaluation
/// - Open/closed principle: Extensible for new flag types and rules
/// - Dependency inversion: Uses abstract storage and evaluation interfaces
/// - Interface segregation: Specific methods for different flag operations
class FeatureFlagService {
  static final FeatureFlagService _instance = FeatureFlagService._internal();
  factory FeatureFlagService() => _instance;
  FeatureFlagService._internal();

  final Map<String, FeatureFlagConfig> _flags = {};
  final Map<String, bool> _overrides = {};
  final StreamController<FeatureFlagEvent> _eventController = StreamController.broadcast();
  SharedPreferences? _prefs;
  bool _initialized = false;

  /// Stream of feature flag events
  Stream<FeatureFlagEvent> get events => _eventController.stream;

  /// Initialize feature flag service
  ///
  /// **Usage:**
  /// ```dart
  /// await FeatureFlagService().initialize();
  /// ```
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();

      // Load default configurations
      for (final config in MigrationFeatureFlags.getAllConfigs()) {
        _flags[config.key] = config;
      }

      // Load persisted overrides
      await _loadPersistedOverrides();

      // Load remote configurations (if available)
      await _loadRemoteConfigurations();

      _initialized = true;

      _emitEvent(
        FeatureFlagEvent(type: FeatureFlagEventType.initialized, flagKey: 'system', timestamp: DateTime.now()),
      );
    } catch (e) {
      debugPrint('Failed to initialize feature flag service: $e');
    }
  }

  /// Check if feature flag is enabled
  ///
  /// **Usage:**
  /// ```dart
  /// final isEnabled = await FeatureFlagService().isEnabled(
  ///   MigrationFeatureFlags.unifiedProviderEnabled,
  ///   context: context,
  /// );
  /// ```
  Future<bool> isEnabled(String flagKey, {FeatureFlagContext? context, bool useCache = true}) async {
    await _ensureInitialized();

    // Check for developer override
    if (_overrides.containsKey(flagKey)) {
      final overrideValue = _overrides[flagKey]!;
      _emitEvent(
        FeatureFlagEvent(
          type: FeatureFlagEventType.evaluated,
          flagKey: flagKey,
          value: overrideValue,
          source: 'override',
          timestamp: DateTime.now(),
        ),
      );
      return overrideValue;
    }

    final config = _flags[flagKey];
    if (config == null) {
      debugPrint('Feature flag not found: $flagKey');
      return false;
    }

    // Check if flag is expired
    if (config.expiresAt != null && DateTime.now().isAfter(config.expiresAt!)) {
      return config.defaultValue;
    }

    // Evaluate flag based on type and rules
    final value = await _evaluateFlag(config, context);

    _emitEvent(
      FeatureFlagEvent(
        type: FeatureFlagEventType.evaluated,
        flagKey: flagKey,
        value: value,
        source: 'evaluation',
        timestamp: DateTime.now(),
        context: context?.toJson(),
      ),
    );

    return value;
  }

  /// Set developer override for feature flag
  ///
  /// **Usage:**
  /// ```dart
  /// await FeatureFlagService().setOverride(
  ///   MigrationFeatureFlags.unifiedProviderEnabled,
  ///   true,
  /// );
  /// ```
  Future<void> setOverride(String flagKey, bool value) async {
    await _ensureInitialized();

    _overrides[flagKey] = value;
    await _persistOverride(flagKey, value);

    _emitEvent(
      FeatureFlagEvent(
        type: FeatureFlagEventType.overrideSet,
        flagKey: flagKey,
        value: value,
        timestamp: DateTime.now(),
      ),
    );
  }

  /// Remove developer override for feature flag
  Future<void> removeOverride(String flagKey) async {
    await _ensureInitialized();

    _overrides.remove(flagKey);
    await _removePersistedOverride(flagKey);

    _emitEvent(
      FeatureFlagEvent(type: FeatureFlagEventType.overrideRemoved, flagKey: flagKey, timestamp: DateTime.now()),
    );
  }

  /// Get all feature flag states
  Future<Map<String, bool>> getAllFlags({FeatureFlagContext? context}) async {
    await _ensureInitialized();

    final results = <String, bool>{};

    for (final flagKey in _flags.keys) {
      results[flagKey] = await isEnabled(flagKey, context: context);
    }

    return results;
  }

  /// Get feature flag configuration
  FeatureFlagConfig? getConfig(String flagKey) {
    return _flags[flagKey];
  }

  /// Update feature flag configuration
  Future<void> updateConfig(FeatureFlagConfig config) async {
    await _ensureInitialized();

    _flags[config.key] = config;
    await _persistConfig(config);

    _emitEvent(
      FeatureFlagEvent(type: FeatureFlagEventType.configUpdated, flagKey: config.key, timestamp: DateTime.now()),
    );
  }

  /// Emergency disable all flags
  Future<void> emergencyDisableAll() async {
    await _ensureInitialized();

    for (final flagKey in _flags.keys) {
      await setOverride(flagKey, false);
    }

    _emitEvent(
      FeatureFlagEvent(type: FeatureFlagEventType.emergencyDisabled, flagKey: 'all', timestamp: DateTime.now()),
    );
  }

  /// Dispose resources
  void dispose() {
    _eventController.close();
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Ensure service is initialized
  Future<void> _ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
  }

  /// Evaluate feature flag based on configuration
  Future<bool> _evaluateFlag(FeatureFlagConfig config, FeatureFlagContext? context) async {
    switch (config.type) {
      case FeatureFlagType.boolean:
        return config.defaultValue;

      case FeatureFlagType.percentage:
        return _evaluatePercentageFlag(config, context);

      case FeatureFlagType.userBased:
        return _evaluateUserBasedFlag(config, context);

      case FeatureFlagType.abTest:
        return _evaluateABTestFlag(config, context);

      case FeatureFlagType.killSwitch:
        return config.defaultValue;
    }
  }

  /// Evaluate percentage-based flag
  bool _evaluatePercentageFlag(FeatureFlagConfig config, FeatureFlagContext? context) {
    final rolloutPercentage = config.metadata['rolloutPercentage'] as int? ?? 0;

    if (context == null) {
      return rolloutPercentage >= 100;
    }

    // Use user ID hash for consistent percentage evaluation
    final hash = context.userId.hashCode.abs();
    final userPercentage = hash % 100;

    return userPercentage < rolloutPercentage;
  }

  /// Evaluate user-based flag
  bool _evaluateUserBasedFlag(FeatureFlagConfig config, FeatureFlagContext? context) {
    if (context == null) return config.defaultValue;

    // Evaluate rules in priority order
    final sortedRules = config.rules.toList()..sort((a, b) => a.priority.compareTo(b.priority));

    for (final rule in sortedRules) {
      if (_evaluateRule(rule, context)) {
        return rule.value as bool;
      }
    }

    return config.defaultValue;
  }

  /// Evaluate A/B test flag
  bool _evaluateABTestFlag(FeatureFlagConfig config, FeatureFlagContext? context) {
    if (context == null) return config.defaultValue;

    // Simple A/B test based on user ID hash
    final hash = context.userId.hashCode.abs();
    return hash % 2 == 0;
  }

  /// Evaluate rule against context
  bool _evaluateRule(FeatureFlagRule rule, FeatureFlagContext context) {
    switch (rule.condition) {
      case 'userAttribute':
        final attributeKey = rule.criteria.keys.first;
        final expectedValue = rule.criteria[attributeKey];
        return context.userAttributes[attributeKey] == expectedValue;

      case 'deviceAttribute':
        final attributeKey = rule.criteria.keys.first;
        final expectedValue = rule.criteria[attributeKey];
        return context.deviceAttributes[attributeKey] == expectedValue;

      case 'platform':
        final expectedPlatform = rule.criteria['platform'] as String;
        return context.platform == expectedPlatform;

      case 'appVersion':
        final expectedVersion = rule.criteria['appVersion'] as String;
        return context.appVersion == expectedVersion;

      default:
        return false;
    }
  }

  /// Load persisted overrides
  Future<void> _loadPersistedOverrides() async {
    if (_prefs == null) return;

    final overridesJson = _prefs!.getString('feature_flag_overrides');
    if (overridesJson != null) {
      final overrides = jsonDecode(overridesJson) as Map<String, dynamic>;
      _overrides.addAll(overrides.cast<String, bool>());
    }
  }

  /// Load remote configurations
  Future<void> _loadRemoteConfigurations() async {
    // This would integrate with a remote feature flag service
    // For now, we'll use local configurations only
  }

  /// Persist override
  Future<void> _persistOverride(String flagKey, bool value) async {
    if (_prefs == null) return;

    final overrides = Map<String, bool>.from(_overrides);
    await _prefs!.setString('feature_flag_overrides', jsonEncode(overrides));
  }

  /// Remove persisted override
  Future<void> _removePersistedOverride(String flagKey) async {
    if (_prefs == null) return;

    final overrides = Map<String, bool>.from(_overrides);
    await _prefs!.setString('feature_flag_overrides', jsonEncode(overrides));
  }

  /// Persist configuration
  Future<void> _persistConfig(FeatureFlagConfig config) async {
    if (_prefs == null) return;

    await _prefs!.setString('feature_flag_config_${config.key}', jsonEncode(config.toJson()));
  }

  /// Emit feature flag event
  void _emitEvent(FeatureFlagEvent event) {
    _eventController.add(event);
  }
}

/// Feature Flag Event
class FeatureFlagEvent {
  final FeatureFlagEventType type;
  final String flagKey;
  final bool? value;
  final String? source;
  final DateTime timestamp;
  final Map<String, dynamic>? context;

  const FeatureFlagEvent({
    required this.type,
    required this.flagKey,
    this.value,
    this.source,
    required this.timestamp,
    this.context,
  });
}

/// Feature Flag Event Type
enum FeatureFlagEventType { initialized, evaluated, overrideSet, overrideRemoved, configUpdated, emergencyDisabled }

/// Feature Flag Provider
///
/// **Context7 MCP Riverpod Integration:**
/// - Provides reactive feature flag access
/// - Integrates with Riverpod state management
/// - Supports real-time flag updates
/// - Maintains consistency across the app
final featureFlagProvider = FutureProvider.family<bool, String>((ref, flagKey) async {
  final service = FeatureFlagService();
  final context = ref.watch(featureFlagContextProvider);
  return service.isEnabled(flagKey, context: context);
});

/// Feature Flag Context Provider
final featureFlagContextProvider = Provider<FeatureFlagContext>((ref) {
  // This would be populated with actual user and device data
  return const FeatureFlagContext(
    userId: 'default_user',
    deviceId: 'default_device',
    appVersion: '1.0.0',
    platform: 'flutter',
  );
});

/// Feature Flag Helper Extensions
///
/// **Context7 MCP Helper Extensions:**
/// - Provides convenient access to feature flags
/// - Integrates with Riverpod for reactive updates
/// - Supports conditional widget rendering
/// - Maintains type safety and null safety
extension FeatureFlagExtensions on WidgetRef {
  /// Check if feature flag is enabled
  ///
  /// **Usage:**
  /// ```dart
  /// if (ref.isFeatureEnabled(MigrationFeatureFlags.unifiedProviderEnabled)) {
  ///   // Use unified provider
  /// } else {
  ///   // Use legacy provider
  /// }
  /// ```
  bool isFeatureEnabled(String flagKey) {
    final flagAsync = watch(featureFlagProvider(flagKey));
    return flagAsync.when(data: (value) => value, loading: () => false, error: (_, _) => false);
  }

  /// Watch feature flag
  AsyncValue<bool> watchFeatureFlag(String flagKey) {
    return watch(featureFlagProvider(flagKey));
  }

  /// Listen to feature flag changes
  void listenToFeatureFlag(String flagKey, void Function(bool? previous, bool current) listener) {
    listen(featureFlagProvider(flagKey), (previous, next) {
      next.whenData((current) {
        final previousValue = previous?.when(data: (value) => value, loading: () => null, error: (_, _) => null);
        listener(previousValue, current);
      });
    });
  }
}

/// Feature Flag Conditional Widget
///
/// **Context7 MCP Conditional Rendering:**
/// - Renders widgets based on feature flag state
/// - Supports loading and error states
/// - Provides fallback widget options
/// - Maintains consistent UI behavior
class FeatureFlagWidget extends ConsumerWidget {
  /// The feature flag key to evaluate
  final String flagKey;

  /// Builder for when the flag is enabled
  final Widget Function(BuildContext context) enabledBuilder;

  /// Builder for when the flag is disabled
  final Widget Function(BuildContext context)? disabledBuilder;

  /// Builder for loading state
  final Widget Function(BuildContext context)? loadingBuilder;

  /// Builder for error state
  final Widget Function(BuildContext context, Object error)? errorBuilder;

  /// Creates a feature flag conditional widget
  const FeatureFlagWidget({
    super.key,
    required this.flagKey,
    required this.enabledBuilder,
    this.disabledBuilder,
    this.loadingBuilder,
    this.errorBuilder,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final flagAsync = ref.watch(featureFlagProvider(flagKey));

    return flagAsync.when(
      data: (isEnabled) {
        if (isEnabled) {
          return enabledBuilder(context);
        } else {
          return disabledBuilder?.call(context) ?? const SizedBox.shrink();
        }
      },
      loading: () {
        return loadingBuilder?.call(context) ?? const SizedBox.shrink();
      },
      error: (error, _) {
        return errorBuilder?.call(context, error) ?? const SizedBox.shrink();
      },
    );
  }
}
