import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/unified_notification_provider.dart';

/// Unified Permission Request Dialog
///
/// **Task 3.3.2: Implement permission request flows with user-friendly messaging**
///
/// This dialog provides a comprehensive, user-friendly interface for requesting
/// notification permissions following Context7 MCP best practices.
///
/// Features:
/// - Type-specific permission information and rationale
/// - Visual benefits and consequences explanation
/// - Progressive permission request flow
/// - Settings fallback guidance
/// - Accessibility support
/// - Localization ready
class UnifiedPermissionRequestDialog extends ConsumerStatefulWidget {
  final List<PermissionNotificationType> requestedTypes;
  final String? customTitle;
  final String? customDescription;
  final bool showBenefits;
  final bool showConsequences;
  final bool allowSkip;
  final VoidCallback? onPermissionGranted;
  final VoidCallback? onPermissionDenied;

  const UnifiedPermissionRequestDialog({
    super.key,
    required this.requestedTypes,
    this.customTitle,
    this.customDescription,
    this.showBenefits = true,
    this.showConsequences = true,
    this.allowSkip = true,
    this.onPermissionGranted,
    this.onPermissionDenied,
  });

  @override
  ConsumerState<UnifiedPermissionRequestDialog> createState() => _UnifiedPermissionRequestDialogState();
}

class _UnifiedPermissionRequestDialogState extends ConsumerState<UnifiedPermissionRequestDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isRequesting = false;
  int _currentTypeIndex = 0;
  final Map<PermissionNotificationType, bool> _permissionResults = {};

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));

    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentType = widget.requestedTypes[_currentTypeIndex];
    final permissionInfo = _getPermissionInfoForType(currentType);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              contentPadding: EdgeInsets.zero,
              content: Container(
                width: MediaQuery.of(context).size.width * 0.9,
                constraints: const BoxConstraints(maxWidth: 400),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(theme, permissionInfo),
                    _buildContent(theme, permissionInfo),
                    _buildActions(theme),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme, PermissionTypeInfo permissionInfo) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Color(permissionInfo.primaryColor).withOpacity(0.1),
        borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // Progress indicator
          if (widget.requestedTypes.length > 1) _buildProgressIndicator(theme, permissionInfo),

          const SizedBox(height: 16),

          // Icon
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              color: Color(permissionInfo.primaryColor),
              borderRadius: BorderRadius.circular(32),
            ),
            child: Icon(_getIconData(permissionInfo.icon), size: 32, color: Colors.white),
          ),

          const SizedBox(height: 16),

          // Title
          Text(
            widget.customTitle ?? permissionInfo.title,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            widget.customDescription ?? permissionInfo.description,
            style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurface.withOpacity(0.7)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(ThemeData theme, PermissionTypeInfo permissionInfo) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Step ${_currentTypeIndex + 1} of ${widget.requestedTypes.length}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: Color(permissionInfo.primaryColor),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: LinearProgressIndicator(
            value: (_currentTypeIndex + 1) / widget.requestedTypes.length,
            backgroundColor: Color(permissionInfo.primaryColor).withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(Color(permissionInfo.primaryColor)),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(ThemeData theme, PermissionTypeInfo permissionInfo) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Benefits section
          if (widget.showBenefits && permissionInfo.benefits.isNotEmpty) _buildBenefitsSection(theme, permissionInfo),

          // Consequences section
          if (widget.showConsequences && permissionInfo.consequences.isNotEmpty)
            _buildConsequencesSection(theme, permissionInfo),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection(ThemeData theme, PermissionTypeInfo permissionInfo) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.green, size: 20),
            const SizedBox(width: 8),
            Text(
              'Benefits',
              style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: Colors.green),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...permissionInfo.benefits.map(
          (benefit) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(width: 28),
                const Icon(Icons.check, color: Colors.green, size: 16),
                const SizedBox(width: 8),
                Expanded(child: Text(benefit, style: theme.textTheme.bodyMedium)),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildConsequencesSection(ThemeData theme, PermissionTypeInfo permissionInfo) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.warning_outlined, color: Colors.orange, size: 20),
            const SizedBox(width: 8),
            Text(
              'Without Permission',
              style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: Colors.orange),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...permissionInfo.consequences.map(
          (consequence) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(width: 28),
                const Icon(Icons.close, color: Colors.orange, size: 16),
                const SizedBox(width: 8),
                Expanded(child: Text(consequence, style: theme.textTheme.bodyMedium)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActions(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Primary action button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isRequesting ? null : _handleAllowPressed,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
              child: _isRequesting
                  ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(strokeWidth: 2))
                  : const Text('Allow Notifications', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            ),
          ),

          const SizedBox(height: 12),

          // Secondary action button
          if (widget.allowSkip)
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: _isRequesting ? null : _handleSkipPressed,
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text(
                  widget.requestedTypes.length > 1 ? 'Skip This Step' : 'Not Now',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _handleAllowPressed() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      final currentType = widget.requestedTypes[_currentTypeIndex];

      // Request permission using unified notification provider
      final result = await ref
          .read(unifiedNotificationSettingsNotifierProvider.notifier)
          .requestPermissions(
            [currentType],
            showRationale: false, // We're already showing rationale
            fallbackToSettings: true,
          );

      _permissionResults[currentType] = result.isSuccess;

      if (result.isSuccess) {
        widget.onPermissionGranted?.call();
        _handleNextStep();
      } else {
        widget.onPermissionDenied?.call();
        _showPermissionDeniedDialog();
      }
    } catch (e) {
      _showErrorDialog(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isRequesting = false;
        });
      }
    }
  }

  void _handleSkipPressed() {
    final currentType = widget.requestedTypes[_currentTypeIndex];
    _permissionResults[currentType] = false;
    widget.onPermissionDenied?.call();
    _handleNextStep();
  }

  void _handleNextStep() {
    if (_currentTypeIndex < widget.requestedTypes.length - 1) {
      setState(() {
        _currentTypeIndex++;
      });
      _animationController.reset();
      _animationController.forward();
    } else {
      _completePermissionFlow();
    }
  }

  void _completePermissionFlow() {
    final grantedCount = _permissionResults.values.where((granted) => granted).length;
    Navigator.of(context).pop(_permissionResults);

    // Show completion message
    if (grantedCount == widget.requestedTypes.length) {
      _showSuccessMessage('All permissions granted! You\'re all set.');
    } else if (grantedCount > 0) {
      _showInfoMessage('$grantedCount of ${widget.requestedTypes.length} permissions granted.');
    }
  }

  void _showPermissionDeniedDialog() {
    // This would show a dialog explaining how to enable permissions in settings
    // Implementation would integrate with the settings guidance system
  }

  void _showErrorDialog(String error) {
    // This would show an error dialog
    // Implementation would integrate with the error handling system
  }

  void _showSuccessMessage(String message) {
    // This would show a success snackbar or toast
    // Implementation would integrate with the messaging system
  }

  void _showInfoMessage(String message) {
    // This would show an info snackbar or toast
    // Implementation would integrate with the messaging system
  }

  PermissionTypeInfo _getPermissionInfoForType(PermissionNotificationType type) {
    // This would use the same logic as in the unified notification provider
    // For now, return a basic implementation
    switch (type) {
      case PermissionNotificationType.local:
        return const PermissionTypeInfo(
          title: 'Local Notifications',
          description: 'Show prayer time reminders and alerts on your device',
          benefits: ['Never miss prayer times', 'Customizable timing', 'Works offline'],
          consequences: ['No prayer alerts', 'Miss reminders'],
          icon: 'notifications',
          primaryColor: 0xFF2196F3,
        );
      default:
        return const PermissionTypeInfo(
          title: 'Notifications',
          description: 'Enable notifications for the best experience',
          benefits: ['Stay informed', 'Get timely updates'],
          consequences: ['Miss important updates'],
          icon: 'notifications',
          primaryColor: 0xFF2196F3,
        );
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'notifications':
        return Icons.notifications_outlined;
      case 'cloud_queue':
        return Icons.cloud_queue;
      case 'schedule':
        return Icons.schedule;
      case 'layers':
        return Icons.layers;
      case 'priority_high':
        return Icons.priority_high;
      case 'notifications_paused':
        return Icons.notifications_paused;
      default:
        return Icons.notifications_outlined;
    }
  }
}
